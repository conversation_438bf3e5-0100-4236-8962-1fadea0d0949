# Helpers

Kohana comes with many static "helper" functions to make certain tasks easier.

You can make your own helpers by simply making a class and putting it in the `classes` directory, and you can also extend any helper to modify or add new functions using transparent extension.

 - **[Arr]** - Array functions.  Get an array key or default to a set value, get an array key by path, etc.

 - **[CLI]** - Parse command line options.

 - **[<PERSON><PERSON>]** - Covered in more detail on the [Cookies](cookies) page.

 - **[Date]** - Useful date functions and constants. Time between two dates, convert between am/pm and military, date offset, etc.
 
 - **[Encrypt]** - Covered in more detail on the [Security](security) page.
 
 - **[Feed]** - Parse and create RSS feeds.
 
 - **[File]** - Get file type by mime, split and merge a file into small pieces.
 
 - **[Form]** - Create HTML form elements. 
 
 - **[Fragment]** - Simple file based caching. Covered in more detail on the [Fragments](fragments) page.

 - **[HTML]** - Useful HTML functions. Encode, obfuscate, create script, anchor, and image tags, etc.
 
 - **[I18n]** - Internationalization helper for creating multilanguage sites.
 
 - **[Inflector]** - Change a word into plural or singular form, camelize or humanize a phrase, etc.
 
 - **[Kohana]** - The Kohana class is also a helper.  Debug variables (like print_r but better), file loading, etc.
 
 - **[Num]** - Provides locale aware formating and english ordinals (th, st, nd, etc).
 
 - **[Profiler]** - Covered in more detail on the [Profiling](profiling) page.

 - **[Remote]** - Remote server access helper using [CURL](http://php.net/curl).
 
 - **[Request]** - Get the current request url, create expire tags, send a file, get the user agent, etc. 
 
 - **[Route]** - Create routes, create an internal link using a route.
 
 - **[Security]** - Covered in more detail on the [Security](security) page.
 
 - **[Session]** - Covered in more detail on the [Sessions](sessions) page.
 
 - **[Text]** - Autolink, prevent window words, convert a number to text, etc.
 
 - **[URL]** - Create a relative or absolute URL, make a URL-safe title, etc.
 
 - **[UTF8]** - Provides multi-byte aware string functions like strlen, strpos, substr, etc.
 
 - **[Upload]** - Helper for uploading files from a form.
