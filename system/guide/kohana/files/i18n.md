# I18n

Kohana has a fairly simple and easy to use i18n system. It is slightly modeled after gettext, but is not as featureful. If you need the features of gettext, please use that :)

## __()

<PERSON><PERSON> has a __() function to do your translations for you. This function is only meant for small sections of text, not entire paragraphs or pages of translated text.

To echo a translated string:

	<?php echo __('Hello, world!');?>

This will echo 'Home' unless you've changed the defined language, which is explained below.

## Changing the displayed language

Use the I18n::lang() method to change the displayed language:

	I18n::lang('fr');

This will change the language to 'es-es'.

## Defining language files

To define the language file for the above language change, create a `i18n/fr.php` that contains:

	<?php
	
	return array
	(
		'Hello, world!' => 'Bonjour, monde!',
	);

Now when you do `__('Hello, world!')`, you will get `Bonjour, monde!`

## I18n variables

You can define variables in your __() calls like so:

	echo __('Hello, :user', array(':user' => $username));

Your i18n key in your translation file will need to be defined as:

	<?php
	
	return array
	(
		'Hello, :user' => 'Bonjour, :user',
	);

## Defining your own __() function

You can define your own __() function by simply defining your own i18n class:

	<?php
	
	class I18n extends Kohana_I18n
	{
		// Intentionally empty
	}
	
	function __($string, array $values = NULL, $lang = 'en-us')
	{
		// Your functionality here
	}

This will cause the built-in __() function to be ignored.