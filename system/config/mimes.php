<?php
/**
 * A list of mime types. Our list is generally more complete and accurate than
 * the operating system MIME list.
 *
 * If there are any missing options, please create a ticket on our issue tracker,
 * http://kohanaphp.com/trac/newticket. Be sure to give the filename and
 * expected MIME type, as well as any additional information you can provide.
 */
return [
	'323'      => ['text/h323'],
	'7z'       => ['application/x-7z-compressed'],
	'abw'      => ['application/x-abiword'],
	'acx'      => ['application/internet-property-stream'],
	'ai'       => ['application/postscript'],
	'aif'      => ['audio/x-aiff'],
	'aifc'     => ['audio/x-aiff'],
	'aiff'     => ['audio/x-aiff'],
	'amf'      => ['application/x-amf'],
	'appcache' => ['text/cache-manifest'],
	'asf'      => ['video/x-ms-asf'],
	'asr'      => ['video/x-ms-asf'],
	'asx'      => ['video/x-ms-asf'],
	'atom'     => ['application/atom+xml'],
	'avi'      => ['video/avi', 'video/msvideo', 'video/x-msvideo'],
	'bin'      => ['application/octet-stream','application/macbinary'],
	'bmp'      => ['image/bmp'],
	'c'        => ['text/x-csrc'],
	'c++'      => ['text/x-c++src'],
	'cab'      => ['application/x-cab'],
	'cc'       => ['text/x-c++src'],
	'cda'      => ['application/x-cdf'],
	'class'    => ['application/octet-stream'],
	'cpp'      => ['text/x-c++src'],
	'cpt'      => ['application/mac-compactpro'],
	'csh'      => ['text/x-csh'],
	'css'      => ['text/css'],
	'csv'      => ['text/x-comma-separated-values', 'application/vnd.ms-excel', 'text/comma-separated-values', 'text/csv'],
	'dbk'      => ['application/docbook+xml'],
	'dcr'      => ['application/x-director'],
	'deb'      => ['application/x-debian-package'],
	'diff'     => ['text/x-diff'],
	'dir'      => ['application/x-director'],
	'divx'     => ['video/divx'],
	'dll'      => ['application/octet-stream', 'application/x-msdos-program'],
	'dmg'      => ['application/x-apple-diskimage'],
	'dms'      => ['application/octet-stream'],
	'doc'      => ['application/msword'],
	'docx'     => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
	'dvi'      => ['application/x-dvi'],
	'dxr'      => ['application/x-director'],
	'eml'      => ['message/rfc822'],
	'eps'      => ['application/postscript'],
	'evy'      => ['application/envoy'],
	'exe'      => ['application/x-msdos-program', 'application/octet-stream'],
	'fla'      => ['application/octet-stream'],
	'flac'     => ['application/x-flac'],
	'flc'      => ['video/flc'],
	'fli'      => ['video/fli'],
	'flv'      => ['video/x-flv'],
	'gif'      => ['image/gif'],
	'gtar'     => ['application/x-gtar'],
	'gz'       => ['application/x-gzip'],
	'h'        => ['text/x-chdr'],
	'h++'      => ['text/x-c++hdr'],
	'hh'       => ['text/x-c++hdr'],
	'hpp'      => ['text/x-c++hdr'],
	'hqx'      => ['application/mac-binhex40'],
	'hs'       => ['text/x-haskell'],
	'htm'      => ['text/html'],
	'html'     => ['text/html'],
	'ico'      => ['image/x-icon'],
	'ics'      => ['text/calendar'],
	'iii'      => ['application/x-iphone'],
	'ins'      => ['application/x-internet-signup'],
	'iso'      => ['application/x-iso9660-image'],
	'isp'      => ['application/x-internet-signup'],
	'jar'      => ['application/java-archive'],
	'java'     => ['application/x-java-applet'],
	'jpe'      => ['image/jpeg', 'image/pjpeg'],
	'jpeg'     => ['image/jpeg', 'image/pjpeg'],
	'jpg'      => ['image/jpeg', 'image/pjpeg'],
	'js'       => ['application/javascript'],
	'json'     => ['application/json'],
	'latex'    => ['application/x-latex'],
	'lha'      => ['application/octet-stream'],
	'log'      => ['text/plain', 'text/x-log'],
	'lzh'      => ['application/octet-stream'],
	'm4a'      => ['audio/mpeg'],
	'm4p'      => ['video/mp4v-es'],
	'm4v'      => ['video/mp4'],
	'man'      => ['application/x-troff-man'],
	'mdb'      => ['application/x-msaccess'],
	'midi'     => ['audio/midi'],
	'mid'      => ['audio/midi'],
	'mif'      => ['application/vnd.mif'],
	'mka'      => ['audio/x-matroska'],
	'mkv'      => ['video/x-matroska'],
	'mov'      => ['video/quicktime'],
	'movie'    => ['video/x-sgi-movie'],
	'mp2'      => ['audio/mpeg'],
	'mp3'      => ['audio/mpeg'],
	'mp4'      => ['application/mp4','audio/mp4','video/mp4'],
	'mpa'      => ['video/mpeg'],
	'mpe'      => ['video/mpeg'],
	'mpeg'     => ['video/mpeg'],
	'mpg'      => ['video/mpeg'],
	'mpg4'     => ['video/mp4'],
	'mpga'     => ['audio/mpeg'],
	'mpp'      => ['application/vnd.ms-project'],
	'mpv'      => ['video/x-matroska'],
	'mpv2'     => ['video/mpeg'],
	'ms'       => ['application/x-troff-ms'],
	'msg'      => ['application/msoutlook','application/x-msg'],
	'msi'      => ['application/x-msi'],
	'nws'      => ['message/rfc822'],
	'oda'      => ['application/oda'],
	'odb'      => ['application/vnd.oasis.opendocument.database'],
	'odc'      => ['application/vnd.oasis.opendocument.chart'],
	'odf'      => ['application/vnd.oasis.opendocument.forumla'],
	'odg'      => ['application/vnd.oasis.opendocument.graphics'],
	'odi'      => ['application/vnd.oasis.opendocument.image'],
	'odm'      => ['application/vnd.oasis.opendocument.text-master'],
	'odp'      => ['application/vnd.oasis.opendocument.presentation'],
	'ods'      => ['application/vnd.oasis.opendocument.spreadsheet'],
	'odt'      => ['application/vnd.oasis.opendocument.text'],
	'oga'      => ['audio/ogg'],
	'ogg'      => ['application/ogg'],
	'ogv'      => ['video/ogg'],
	'otg'      => ['application/vnd.oasis.opendocument.graphics-template'],
	'oth'      => ['application/vnd.oasis.opendocument.web'],
	'otp'      => ['application/vnd.oasis.opendocument.presentation-template'],
	'ots'      => ['application/vnd.oasis.opendocument.spreadsheet-template'],
	'ott'      => ['application/vnd.oasis.opendocument.template'],
	'p'        => ['text/x-pascal'],
	'pas'      => ['text/x-pascal'],
	'patch'    => ['text/x-diff'],
	'pbm'      => ['image/x-portable-bitmap'],
	'pdf'      => ['application/pdf', 'application/x-download'],
	'php'      => ['application/x-httpd-php'],
	'php3'     => ['application/x-httpd-php'],
	'php4'     => ['application/x-httpd-php'],
	'php5'     => ['application/x-httpd-php'],
	'phps'     => ['application/x-httpd-php-source'],
	'phtml'    => ['application/x-httpd-php'],
	'pl'       => ['text/x-perl'],
	'pm'       => ['text/x-perl'],
	'png'      => ['image/png', 'image/x-png'],
	'po'       => ['text/x-gettext-translation'],
	'pot'      => ['application/vnd.ms-powerpoint'],
	'pps'      => ['application/vnd.ms-powerpoint'],
	'ppt'      => ['application/powerpoint'],
	'pptx'     => ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
	'ps'       => ['application/postscript'],
	'psd'      => ['application/x-photoshop', 'image/x-photoshop'],
	'pub'      => ['application/x-mspublisher'],
	'py'       => ['text/x-python'],
	'qt'       => ['video/quicktime'],
	'ra'       => ['audio/x-realaudio'],
	'ram'      => ['audio/x-realaudio', 'audio/x-pn-realaudio'],
	'rar'      => ['application/rar'],
	'rgb'      => ['image/x-rgb'],
	'rm'       => ['audio/x-pn-realaudio'],
	'rpm'      => ['audio/x-pn-realaudio-plugin', 'application/x-redhat-package-manager'],
	'rss'      => ['application/rss+xml'],
	'rtf'      => ['text/rtf'],
	'rtx'      => ['text/richtext'],
	'rv'       => ['video/vnd.rn-realvideo'],
	'sea'      => ['application/octet-stream'],
	'sh'       => ['text/x-sh'],
	'shtml'    => ['text/html'],
	'sit'      => ['application/x-stuffit'],
	'smi'      => ['application/smil'],
	'smil'     => ['application/smil'],
	'so'       => ['application/octet-stream'],
	'src'      => ['application/x-wais-source'],
	'svg'      => ['image/svg+xml'],
	'swf'      => ['application/x-shockwave-flash'],
	't'        => ['application/x-troff'],
	'tar'      => ['application/x-tar'],
	'tcl'      => ['text/x-tcl'],
	'tex'      => ['application/x-tex'],
	'text'     => ['text/plain'],
	'texti'    => ['application/x-texinfo'],
	'textinfo' => ['application/x-texinfo'],
	'tgz'      => ['application/x-tar'],
	'tif'      => ['image/tiff'],
	'tiff'     => ['image/tiff'],
	'torrent'  => ['application/x-bittorrent'],
	'tr'       => ['application/x-troff'],
	'tsv'      => ['text/tab-separated-values'],
	'txt'      => ['text/plain'],
	'wav'      => ['audio/x-wav'],
	'wax'      => ['audio/x-ms-wax'],
	'wbxml'    => ['application/wbxml'],
	'webapp'   => ['application/x-web-app-manifest+json'],
	'webm'     => ['video/webm'],
	'wm'       => ['video/x-ms-wm'],
	'wma'      => ['audio/x-ms-wma'],
	'wmd'      => ['application/x-ms-wmd'],
	'wmlc'     => ['application/wmlc'],
	'wmv'      => ['video/x-ms-wmv', 'application/octet-stream'],
	'wmx'      => ['video/x-ms-wmx'],
	'wmz'      => ['application/x-ms-wmz'],
	'word'     => ['application/msword', 'application/octet-stream'],
	'wp5'      => ['application/wordperfect5.1'],
	'wpd'      => ['application/vnd.wordperfect'],
	'wvx'      => ['video/x-ms-wvx'],
	'xbm'      => ['image/x-xbitmap'],
	'xcf'      => ['image/xcf'],
	'xhtml'    => ['application/xhtml+xml'],
	'xht'      => ['application/xhtml+xml'],
	'xl'       => ['application/excel', 'application/vnd.ms-excel'],
	'xla'      => ['application/excel', 'application/vnd.ms-excel'],
	'xlc'      => ['application/excel', 'application/vnd.ms-excel'],
	'xlm'      => ['application/excel', 'application/vnd.ms-excel'],
	'xls'      => ['application/excel', 'application/vnd.ms-excel'],
	'xlsx'     => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
	'xlt'      => ['application/excel', 'application/vnd.ms-excel'],
	'xml'      => ['text/xml', 'application/xml'],
	'xof'      => ['x-world/x-vrml'],
	'xpm'      => ['image/x-xpixmap'],
	'xsl'      => ['text/xml'],
	'xvid'     => ['video/x-xvid'],
	'xwd'      => ['image/x-xwindowdump'],
	'z'        => ['application/x-compress'],
	'zip'      => ['application/x-zip', 'application/zip', 'application/x-zip-compressed']
];
