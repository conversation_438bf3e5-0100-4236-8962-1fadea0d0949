#!/bin/bash
#
# This script is similar to minion but will do a few additional things:
# - PHP process is run in the background
# - PHP process is monitored and restarted if it exits for any reason
# - Added handlers for SUGHUP, SIGINT, and SIGTERM
#
# This is meant for long running minion tasks (like background workers).
# Shutting down the minion tasks is done by sending a SIGINT or SIGTERM signal
# to this miniond process. You can also restart the minion task by sending a
# SUGHUP signal to this process. It's useful to restart all your workers when
# deploying new code so that the workers reload their code as well.
# You cannot use this script for tasks that require user input because of the
# PHP process running in the background.
#
# Usage: ./miniond [task:name] [--option1=optval1 --option2=optval2]
#
# And so on.
#

# Define some functions
function start_daemon()
{
	echo "Starting"
	./minion $ARGS & # This assumes miniond is sitting next to minion
	pid=$! # Store pid (globally) for later use..
}

function stop_daemon()
{
	kill -TERM $pid
	wait $pid # Wait for the task to exit and store the exit code
	ecode=$?  # Store exit code (globally) for later use..
}

function handle_SIGHUP()
{
	echo "Restarting"
	stop_daemon
	start_daemon
}

function handle_SIGTERM_SIGINT()
{
	echo "Shutting Down"
	stop_daemon
	exit $ecode
}

# Register signal handlers
trap handle_SIGHUP SIGHUP
trap handle_SIGTERM_SIGINT SIGTERM SIGINT

ARGS=$@

start_daemon

while :
do
	# Pauses the script until $pid is dead
	wait $pid

	# Make sure someone didn't start it back up already (SIGHUP handler does this)
	if ! kill -0 $pid > /dev/null 2>&1
	then
		start_daemon
	fi
done