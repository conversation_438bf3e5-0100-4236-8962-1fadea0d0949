<!--
	This is an example phpunit.xml file to get you started
	Copy it to a directory, update the relative paths and rename to phpunit.xml
	Then to run tests cd into it's directory and just run
		phpunit
	(it'll automatically use any phpunit.xml file in the current directory)

	Any options you specify when calling phpunit will override the ones in here
-->
<phpunit colors="true" bootstrap="rel/path/to/index.php">
	<testsuites>
		<testsuite name="Kohana Tests">
			<file>rel/path/to/unittest/tests.php</file>
		</testsuite>
	</testsuites>
</phpunit>
