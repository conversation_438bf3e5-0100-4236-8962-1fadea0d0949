# Examples

- [Simple](examples/simple): Basic, one table model examples.
- [Validation](examples/validation): Full example of creating a user account and handling validation errors.

## @TODO:

The following is a sample list of examples that might be useful.  Don't feel limited by this list, or consider these required.  Items on the list can be combined, split up, removed or added to.  All contribution are appreciated.

- Examples of changing things like $_table_name, $_labels, with, etc.
- Example of a one to one relationship.
- Example of one to many
- Example of many to many.
