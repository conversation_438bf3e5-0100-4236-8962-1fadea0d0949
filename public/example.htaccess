# Turn on URL rewriting
RewriteEngine On

###Hides server version on Apache
###http://www.ducea.com/2006/06/15/apache-tips-tricks-hide-apache-software-version/
#ServerTokens ProductOnly
#ServerSignature Off

###Forces https
<IfModule mod_rewrite.c>
    #RewriteCond %{SERVER_PORT} !^443
    #RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    ###Set certificate pinning
    ###https://www.owasp.org/index.php/Certificate_and_Public_Key_Pinning
    <IfModule mod_headers.c>
        #Header set Public-Key-Pins "max-age=500; includeSubDomains; pin-sha256=\"\"; report-uri=\"report-uri\"";
    </IfModule>

    ###Sets HSTS (Strict-Transport-Security)
    ###https://www.owasp.org/index.php/HTTP_Strict_Transport_Security
    <IfModule mod_headers.c>
        #Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS
    </IfModule>
</IfModule>

<IfModule mod_headers.c>
    ###Disable displaying webpage in Iframes -> prevents Clickjacking
    ###https://www.owasp.org/index.php/Clickjacking
    #Header set X-Frame-Options SAMEORIGIN

    ###Block requests with incorrect MIME-types
    ###https://www.owasp.org/index.php/OWASP_Secure_Headers_Project#X-Content-Type-Options
    #Header set X-Content-Type-Options "nosniff"

    ###Set IE-8 XSS Filter on
    ###https://www.owasp.org/index.php/OWASP_Secure_Headers_Project#X-XSS-Protection
    #Header set X-XSS-Protection "1; mode=block"

    ###Disable Flash cross-domain requests
    ###https://www.owasp.org/index.php/OWASP_Secure_Headers_Project#X-Permitted-Cross-Domain-Policies
    #Header set X-Permitted-Cross-Domain-Policies "none"
</IfModule>

# Installation directory
RewriteBase /

# Protect hidden files from being viewed
<Files .*>
	Order Deny,Allow
	Deny From All
</Files>

# Allow any files or directories that exist to be displayed directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Rewrite all other URLs to index.php/URL
# godaddy hack no input file specified
RewriteRule .* index.php [PT,QSA,L]
