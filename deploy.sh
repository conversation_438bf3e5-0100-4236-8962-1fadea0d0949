#!/bin/bash
# arquivo gerado automaticamente por create-koseven.sh

docker service rm superresponse

sleep 3

docker service create \
    --replicas 1 \
    --name superresponse  \
    --network superbot-network  \
    --label  traefik.enable=true   \
    --label  'traefik.http.routers.superresponse.rule=Host(`superresponse.internut.com.br`)'  \
    --label  traefik.http.routers.superresponse.entrypoints=websecure  \
    --label  traefik.http.routers.superresponse.tls=true   \
    --label  traefik.http.routers.superresponse.tls.certresolver=myresolver \
    --label  traefik.http.services.superresponse.loadbalancer.server.port=80   \
    --mount type=bind,source=/home/<USER>/environment/Office/Apps/inProduction/chat/superresponse,destination=/var/www/html \
    --mount type=bind,source=/home/<USER>/environment/Office/Configs/dev/config.env,destination=/data/config.env \
    --mount type=bind,source=/home/<USER>/environment/Office/Configs/nginx.conf,destination=/etc/nginx/nginx.conf \
koseven:latest
