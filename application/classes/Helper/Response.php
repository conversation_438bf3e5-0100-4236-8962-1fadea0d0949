<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Helper para formatação de respostas
 */
class Helper_Response {
    /**
     * Formata uma resposta de erro
     *
     * @param string $message Mensagem de erro
     * @param int $code Código HTTP (default: 400)
     * @param array $data Dados adicionais (opcional)
     * @return array Resposta formatada
     */
    public static function error($message, $code = 400, $data = []) {
        $response = [
            'status' => 'error',
            'message' => $message
        ];
        
        if (!empty($data)) {
            $response = array_merge($response, $data);
        }
        
        return [
            'body' => $response,
            'code' => $code
        ];
    }
    
    /**
     * Formata uma resposta de sucesso
     *
     * @param string $message Mensagem de sucesso
     * @param array $data Dados adicionais (opcional)
     * @return array Resposta formatada
     */
    public static function success($message, $data = []) {
        $response = [
            'status' => 'success',
            'message' => $message
        ];
        
        if (!empty($data)) {
            $response = array_merge($response, $data);
        }
        
        return [
            'body' => $response,
            'code' => 200
        ];
    }
}
