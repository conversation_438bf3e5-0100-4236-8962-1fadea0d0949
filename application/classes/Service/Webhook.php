<?php defined('SYSPATH') or die('No direct script access.');

class Service_Webhook {
    
    private $webhook_url = 'https://n8n.internut.com.br/webhook/94f88f74-fbfd-4b66-82d6-badd3bb465ca';
    
    public function send_message($message_text)
    {
        $payload = json_encode(['mensagem' => $message_text]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->webhook_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        if ($http_code >= 400) {
            throw new Exception("HTTP Error: $http_code");
        }
        
        return json_decode($response, true);
    }
}