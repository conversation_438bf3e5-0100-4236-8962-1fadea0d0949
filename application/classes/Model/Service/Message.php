<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Serviço para processamento de mensagens
 */
class Model_Service_Message {
    /**
     * Processa uma mensagem através do webhook de IA
     *
     * @param string $message Mensagem a ser processada
     * @return array Resposta processada
     */
    public function process($message) {
        $webhook = new Model_Api_Webhook();
        $response = $webhook->send_message($message);
        
        if ($response['status'] === 'error') {
            return $response;
        }
        
        // Processa a resposta
        $response_data = json_decode($response['response'], true);
        $result = [
            'status' => 'success',
            'message' => Kohana::message('messages', 'success.message_sent'),
            'http_code' => $response['http_code'],
            'response' => $response['response'],
            'ia' => $response['response'],
            'sent_data' => ['mensagem' => $message]
        ];
        
        // Verifica se a resposta contém dados JSON
        if ($response_data && isset($response_data['text'])) {
            // Extrai o JSON do campo text
            $text_json = $this->_extract_json_from_text($response_data['text']);
            
            // Verifica se deve acionar a API de produtos
            if ($this->_should_trigger_product_api($text_json)) {
                $product_service = new Model_Service_Product();
                $product_result = $product_service->search_by_model($text_json['produto']);
                
                if ($product_result['status'] === 'success') {
                    $result['products'] = $product_result['products'];
                    $result['formatted_response'] = $product_result['formatted_response'];
                } else {
                    $result['product_error'] = $product_result['message'];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Extrai JSON de um texto que pode conter marcadores de código
     *
     * @param string $text Texto contendo JSON
     * @return array|null Dados JSON extraídos ou null
     */
    protected function _extract_json_from_text($text) {
        $clean_text = trim(str_replace(['```json', '```'], '', $text));
        return json_decode($clean_text, true);
    }
    
    /**
     * Verifica se a resposta deve acionar a API de produtos
     *
     * @param array|null $json_data Dados JSON extraídos
     * @return boolean True se deve acionar a API de produtos
     */
    protected function _should_trigger_product_api($json_data) {
        if (!$json_data || !isset($json_data['tipo']) || !isset($json_data['produto'])) {
            return false;
        }
        
        $config = Kohana::$config->load('messages.product_types');
        return in_array(strtolower($json_data['tipo']), $config['trigger_types']);
    }
}
