<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Serviço para envio de mensagens via WhatsApp
 */
class Model_Service_WhatsApp {
    /**
     * Envia uma mensagem via WhatsApp
     *
     * @param string $to Número do destinatário
     * @param string $message Mensagem a ser enviada
     * @param string $sender ID do remetente (opcional)
     * @return array Resultado do envio
     */
    public function send_message($to, $message, $sender = null) {
        $whatsapp_api = new Model_Api_WhatsApp();
        $response = $whatsapp_api->send_message($to, $message, $sender);
        
        return [
            'status' => $response['status'],
            'message' => $response['message'],
            'response' => $response['response'],
            'endpoint' => $this->_get_endpoint($sender),
            'recipient' => $to
        ];
    }
    
    /**
     * Obtém o endpoint da API do WhatsApp
     *
     * @param string $sender ID do remetente (opcional)
     * @return string Endpoint completo
     */
    protected function _get_endpoint($sender = null) {
        $config = Kohana::$config->load('apis.whatsapp');
        $sender = $sender ?: $config['default_sender'];
        return $config['base_url'] . $sender . '/send';
    }
}
