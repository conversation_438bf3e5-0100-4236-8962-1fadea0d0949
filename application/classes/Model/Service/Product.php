<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Serviço para processamento de produtos
 */
class Model_Service_Product {
    /**
     * Busca produtos por modelo e formata a resposta
     *
     * @param string $model Modelo do produto
     * @return array Resultado da busca com produtos formatados
     */
    public function search_by_model($model) {
        $product_api = new Model_Api_Product();
        $response = $product_api->search_by_model($model);
        
        if ($response['status'] === 'error') {
            return $response;
        }
        
        $products = json_decode($response['response'], true);
        $formatted_response = $this->_format_products($products);
        
        return [
            'status' => 'success',
            'message' => Kohana::message('messages', 'success.request_successful'),
            'products' => $products,
            'formatted_response' => $formatted_response
        ];
    }
    
    /**
     * Formata a lista de produtos para exibição
     *
     * @param array $products Dados de produtos
     * @return string Produtos formatados em texto
     */
    protected function _format_products($products) {
        $formatted_response = "Produtos encontrados:\n\n";
        
        if (!empty($products['data'])) {
            foreach ($products['data'] as $product) {
                // Formata preço com formato de moeda brasileira
                $price = number_format((float)$product['ProdutoRevenda'], 2, ',', '.');
                $unit = isset($product['ProdutoUnidade']) ? $product['ProdutoUnidade'] : 'un';
                
                // Constrói informações formatadas do produto
                $formatted_response .= $product['ProdutoModelo'] . "\n";
                $formatted_response .= $product['ProdutoNome'] . "\n";
                $formatted_response .= "R$ " . $price . ' ' . $unit . "\n\n";
            }
        } else {
            $formatted_response .= "Nenhum produto encontrado.\n";
        }
        
        return $formatted_response;
    }
}
