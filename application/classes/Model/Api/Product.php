<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Modelo para interação com a API de produtos
 */
class Model_Api_Product extends Model_Api_Client {
    /**
     * Busca produtos por modelo
     *
     * @param string $model Modelo do produto
     * @return array Resposta da API de produtos
     */
    public function search_by_model($model) {
        $config = Kohana::$config->load('apis.products');
        $url = $config['base_url'] . '?search=' . urlencode($model);
        
        $headers = [
            'Content-Type: application/json',
            'X-API-Key: ' . $config['api_key']
        ];
        
        $options = [
            'timeout' => $config['timeout']
        ];
        
        return $this->get($url, $headers, $options);
    }
}
