<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Modelo para interação com a API do WhatsApp
 */
class Model_Api_WhatsApp extends Model_Api_Client {
    /**
     * Envia uma mensagem via WhatsApp
     *
     * @param string $to Número do destinatário
     * @param string $message Mensagem a ser enviada
     * @param string $sender ID do remetente (opcional)
     * @return array Resposta da API do WhatsApp
     */
    public function send_message($to, $message, $sender = null) {
        $config = Kohana::$config->load('apis.whatsapp');
        $sender = $sender ?: $config['default_sender'];
        $url = $config['base_url'] . $sender . '/send';
        
        $payload = json_encode([
            'to' => $to,
            'message' => $message
        ]);
        
        $headers = [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        ];
        
        $options = [
            'timeout' => $config['timeout']
        ];
        
        return $this->post($url, $payload, $headers, $options);
    }
}
