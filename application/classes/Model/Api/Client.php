<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Cliente HTTP base para todas as APIs
 */
class Model_Api_Client {
    /**
     * Configurações padrão
     */
    protected $_default_options = [
        'timeout' => 30,
        'verify_ssl' => false,
    ];
    
    /**
     * Executa uma requisição HTTP POST
     *
     * @param string $url URL da API
     * @param array|string $payload Dados a serem enviados
     * @param array $headers Cabeçalhos HTTP adicionais
     * @param array $options Opções adicionais
     * @return array Resposta formatada
     */
    public function post($url, $payload, $headers = [], $options = []) {
        $options = array_merge($this->_default_options, $options);
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout']);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $options['verify_ssl']);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $options['verify_ssl'] ? 2 : 0);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            
            curl_close($ch);
            
            if ($error) {
                return $this->_error_response(
                    Kohana::message('messages', 'errors.curl_error', $error),
                    0
                );
            }
            
            return $this->_format_response($response, $http_code);
            
        } catch (Exception $e) {
            return $this->_error_response($e->getMessage(), 500);
        }
    }
    
    /**
     * Executa uma requisição HTTP GET
     *
     * @param string $url URL da API
     * @param array $headers Cabeçalhos HTTP adicionais
     * @param array $options Opções adicionais
     * @return array Resposta formatada
     */
    public function get($url, $headers = [], $options = []) {
        $options = array_merge($this->_default_options, $options);
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, $options['timeout']);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $options['verify_ssl']);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $options['verify_ssl'] ? 2 : 0);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            
            curl_close($ch);
            
            if ($error) {
                return $this->_error_response(
                    Kohana::message('messages', 'errors.curl_error', $error),
                    0
                );
            }
            
            return $this->_format_response($response, $http_code);
            
        } catch (Exception $e) {
            return $this->_error_response($e->getMessage(), 500);
        }
    }
    
    /**
     * Formata a resposta da API
     *
     * @param string $response Resposta da API
     * @param int $http_code Código HTTP
     * @return array Resposta formatada
     */
    protected function _format_response($response, $http_code) {
        $success = ($http_code >= 200 && $http_code < 300);
        
        return [
            'status' => $success ? 'success' : 'error',
            'message' => $success 
                ? Kohana::message('messages', 'success.request_successful')
                : Kohana::message('messages', 'errors.http_error', $http_code),
            'http_code' => $http_code,
            'response' => $response
        ];
    }
    
    /**
     * Cria uma resposta de erro
     *
     * @param string $message Mensagem de erro
     * @param int $http_code Código HTTP
     * @return array Resposta de erro formatada
     */
    protected function _error_response($message, $http_code) {
        return [
            'status' => 'error',
            'message' => $message,
            'http_code' => $http_code,
            'response' => null
        ];
    }
}
