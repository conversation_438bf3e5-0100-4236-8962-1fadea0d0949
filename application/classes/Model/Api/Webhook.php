<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Modelo para interação com o webhook de IA
 */
class Model_Api_Webhook extends Model_Api_Client {
    /**
     * Envia uma mensagem para o webhook de IA
     *
     * @param string $message Mensagem a ser enviada
     * @return array Resposta do webhook
     */
    public function send_message($message) {
        $config = Kohana::$config->load('apis.webhook');
        $url = $config['url'];
        $payload = json_encode(['mensagem' => $message]);
        
        $headers = [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        ];
        
        $options = [
            'timeout' => $config['timeout']
        ];
        
        return $this->post($url, $payload, $headers, $options);
    }
}
