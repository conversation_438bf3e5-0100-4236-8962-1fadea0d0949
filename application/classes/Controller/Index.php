<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Controller principal para processamento de mensagens
 */
class Controller_Index extends Controller {
    /**
     * Ação principal para processamento de mensagens
     */
    public function action_index() {
        // Extrai parâmetros da requisição
        $query = $this->request->query('message');
        $send  = $this->request->query('send');
        $to    = $this->request->query('to');

        
        // Valida parâmetros obrigatórios
        if (empty($query)) {
            $response = Helper_Response::error(
                Kohana::message('messages', 'errors.missing_message'),
                400
            );
            
            return $this->_send_json_response($response['code'], $response['body']);
        }
        
        try {
            // Processa a mensagem
            $message_service = new Model_Service_Message();
            $result = $message_service->process($query);

            // Verifica se deve enviar mensagem via WhatsApp
            if (!empty($to) && isset($result['formatted_response'])) {
                $whatsapp_service = new Model_Service_WhatsApp();
                $whatsapp_result = $whatsapp_service->send_message(
                    $to, 
                    $result['formatted_response'],
                    $send
                );
                
                $result['whatsapp'] = $whatsapp_result;
            } else {
                $result['whatsapp'] = [
                    'status' => 'skipped',
                    'message' => 'WhatsApp message not sent: missing "to" parameter or "formatted_response" null',
                    'endpoint' => null,
                    'recipient' => ''
                ];
            }
            
            $response = Helper_Response::success(
                Kohana::message('messages', 'success.message_sent'),
                $result
            );
            
        } catch (Exception $e) {
            $response = Helper_Response::error(
                'Exception: ' . $e->getMessage(),
                500
            );
        }
        
        return $this->_send_json_response($response['code'], $response['body']);
    }
    
    /**
     * Envia uma resposta JSON
     * 
     * @param int $code Código HTTP
     * @param array $data Dados da resposta
     */
    protected function _send_json_response($code, $data) {
        $this->response->headers('Content-Type', 'application/json');
        $this->response->status($code);
        $this->response->body(json_encode($data, JSON_PRETTY_PRINT));
    }
} // End Index
