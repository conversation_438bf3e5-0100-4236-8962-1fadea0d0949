server {
    listen 80;
    server_name _;
    root /var/www/html;
    index index.php index.html index.htm;
    
    # Add more detailed error logging
    error_log /var/log/nginx/error.log debug;
    access_log /var/log/nginx/access.log;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
    
    location ~ /\. {
        deny all;
    }
}
