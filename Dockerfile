FROM alpine:3.18

# Install PHP, Nginx and required extensions
RUN apk add --no-cache \
    nginx \
    php82 \
    php82-fpm \
    php82-json \
    php82-curl \
    php82-mbstring \
    php82-openssl \
    php82-session \
    php82-xml \
    php82-dom \
    php82-pdo \
    php82-pdo_mysql \
    php82-mysqli \
    php82-gd \
    php82-zip \
    php82-fileinfo \
    php82-tokenizer \
    php82-ctype \
    supervisor

# Configure PHP-FPM
RUN sed -i 's/;cgi.fix_pathinfo=1/cgi.fix_pathinfo=0/' /etc/php82/php.ini && \
    sed -i 's/user = nobody/user = nginx/' /etc/php82/php-fpm.d/www.conf && \
    sed -i 's/group = nobody/group = nginx/' /etc/php82/php-fpm.d/www.conf && \
    sed -i 's/listen = 127.0.0.1:9000/listen = 9000/' /etc/php82/php-fpm.d/www.conf

# Configure Nginx
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/http.d/default.conf

# Configure Supervisor
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set working directory
WORKDIR /var/www/html

# Create required directories
RUN mkdir -p /var/www/html/application/logs \
    /var/www/html/application/cache \
    /run/nginx \
    /var/log/supervisor

# Create a simple index.php for testing
RUN echo '<?php phpinfo(); ?>' > /var/www/html/index.php

# Set permissions - make nginx user own everything
RUN chown -R nginx:nginx /var/www/html && \
    chmod -R 755 /var/www/html

EXPOSE 80

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
