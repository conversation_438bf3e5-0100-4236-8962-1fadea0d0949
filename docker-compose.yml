services:
  superresponse:
    image: koseven:latest
    container_name: superresponse
    restart: unless-stopped
    networks:
      - superbot-network
    volumes:
      - /home/<USER>/enviroment/apps/SuperResponse:/var/www/html
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.superresponse.rule=Host(`superresponse.internut.com.br`)"
      - "traefik.http.routers.superresponse.entrypoints=websecure"
      - "traefik.http.routers.superresponse.tls=true"
      - "traefik.http.routers.superresponse.tls.certresolver=myresolver"
      - "traefik.http.services.superresponse.loadbalancer.server.port=80"
      - "traefik.http.routers.superresponse.tls.certresolver=letsencrypt"

networks:
  superbot-network:
    external: true
