# Diagrama de Arquitetura - Refatoração Controller_Index

## Visão Geral da Arquitetura

```
┌─────────────────┐     ┌───────────────────────────────────────────────────┐
│                 │     │                                                   │
│  Controller     │     │               Service Layer                       │
│  Index          │◄────┤                                                   │
│                 │     │                                                   │
└────────┬────────┘     └───────────────────┬───────────────────────────────┘
         │                                  │
         │                                  │
         │                                  ▼
         │              ┌───────────────────────────────────────────────────┐
         │              │                                                   │
         └─────────────►│               Repository Layer                    │
                        │                                                   │
                        │                                                   │
                        └───────────────────────────────────────────────────┘
```

## Fluxo de Execução Simplificado

1. **Controller recebe requisição**
   - Extrai parâmetros (mensagem, send, to)
   - Cria DTOs para encapsular os dados

2. **Controller chama serviços**
   - MessageProcessor processa a mensagem via webhook
   - ProductService busca produtos se necessário
   - WhatsAppService envia mensagens se necessário

3. **Serviços usam repositórios**
   - WebhookRepository faz chamada ao webhook de IA
   - ProductRepository faz chamada à API de produtos
   - WhatsAppRepository faz chamada à API do WhatsApp

4. **Controller formata e retorna resposta**
   - Usa ResponseFormatter para padronizar respostas

## Estrutura de Pastas Simplificada

```
application/
├── classes/
│   ├── Controller/
│   │   └── Index.php
│   ├── DTO/
│   │   ├── MessageRequestDTO.php
│   │   ├── MessageResponseDTO.php
│   │   ├── ProductDTO.php
│   │   └── WhatsAppDTO.php
│   ├── Repository/
│   │   ├── WebhookRepository.php
│   │   ├── ProductRepository.php
│   │   └── WhatsAppRepository.php
│   ├── Service/
│   │   ├── MessageProcessorService.php
│   │   ├── ProductService.php
│   │   ├── WhatsAppService.php
│   │   └── ResponseFormatterService.php
│   └── Factory/
│       └── ServiceFactory.php
```

## Implementação Gradual

Para facilitar a implementação e entendimento, você pode seguir estas etapas:

1. **Fase 1: Criar estrutura básica**
   - Criar pastas para organizar as classes
   - Implementar Factory e DTOs

2. **Fase 2: Implementar repositórios**
   - Extrair chamadas HTTP para repositórios específicos
   - Testar cada repositório isoladamente

3. **Fase 3: Implementar serviços**
   - Criar serviços que usam os repositórios
   - Implementar lógica de negócio nos serviços

4. **Fase 4: Refatorar o controller**
   - Atualizar o controller para usar os serviços
   - Remover código duplicado e simplificar

## Exemplo de Fluxo de Dados

```
Request → Controller → MessageRequestDTO → MessageProcessorService → WebhookRepository → API Externa
                                                                  ↓
                                                        Análise da resposta
                                                                  ↓
                                      Se contém produto → ProductService → ProductRepository → API de Produtos
                                                                  ↓
                                      Se tem destinatário → WhatsAppService → WhatsAppRepository → API WhatsApp
                                                                  ↓
                                                        Formatação da resposta
                                                                  ↓
                                                            Response JSON
```
