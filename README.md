# SuperResponse

## Visão Geral

SuperResponse é uma aplicação de processamento de mensagens baseada no framework Kohana/Koseven que integra serviços de IA, busca de produtos e envio de mensagens via WhatsApp. O sistema recebe consultas de texto, processa-as através de um webhook de IA, e pode realizar buscas em uma API de produtos e enviar respostas formatadas via WhatsApp.

## Arquitetura

O projeto segue uma arquitetura em camadas, com separação clara de responsabilidades:

### Camadas

1. **Controller**: Responsável por receber requisições HTTP e coordenar o fluxo de dados.
2. **Service Layer**: Contém a lógica de negócio e orquestra as chamadas entre diferentes componentes.
3. **API Layer**: Encapsula a comunicação com serviços externos (webhook, API de produtos, WhatsApp).
4. **Helpers**: Utilitários para formatação de respostas e outras funções auxiliares.

### Configuração

O sistema utiliza arquivos de configuração do Kohana para armazenar:

- URLs de APIs e endpoints
- Chaves de API e credenciais
- Mensagens e templates
- Parâmetros de timeout e outras configurações

## Fluxo de Funcionamento

1. O controller recebe uma requisição com uma mensagem de texto
2. A mensagem é enviada para um webhook de IA para processamento
3. A resposta da IA é analisada para identificar intenções (consulta de produtos, etc)
4. Se necessário, uma busca é realizada na API de produtos
5. Os resultados são formatados em uma resposta legível
6. Se solicitado, a resposta é enviada via WhatsApp para o número especificado

## Endpoints

### Principal

```
GET /index
```

**Parâmetros**:
- `mensagem`: Texto a ser processado (obrigatório)
- `to`: Número de telefone para envio via WhatsApp (opcional)
- `send`: ID de sessão do WhatsApp (opcional, usa o padrão se não informado)

## Estrutura de Diretórios

```
/application
  /classes
    /Controller
      Index.php           # Controller principal
    /Model
      /Api                # Clientes de API
        Client.php        # Cliente HTTP base
        Webhook.php       # Cliente para webhook de IA
        Product.php       # Cliente para API de produtos
        WhatsApp.php      # Cliente para API de WhatsApp
      /Service            # Serviços de negócio
        Message.php       # Processamento de mensagens
        Product.php       # Busca e formatação de produtos
        WhatsApp.php      # Envio de mensagens WhatsApp
    /Helper
      Response.php       # Formatação de respostas JSON
  /config
    apis.php             # Configurações de APIs
    messages.php         # Templates de mensagens
```

## Instalação

1. Clone o repositório
2. Configure um servidor web com PHP 7.3+ apontando para o diretório raiz
3. Verifique as configurações em `application/config/`
4. Acesse a aplicação via navegador

## Desenvolvimento

O projeto segue as convenções do framework Kohana/Koseven e utiliza as seguintes práticas:

- Configuração centralizada em arquivos de config
- Separação de responsabilidades (SOLID)
- Tratamento consistente de erros
- Respostas padronizadas

## Licença

Este projeto é licenciado sob a licença BSD, conforme o framework Koseven.
